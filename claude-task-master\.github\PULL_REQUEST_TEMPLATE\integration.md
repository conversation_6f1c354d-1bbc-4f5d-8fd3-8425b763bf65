# 🔌 New Integration

## What tool/IDE is being integrated?

<!-- Name and brief description -->

## What can users do with it?

<!-- Key benefits -->

## How to Enable

### Setup

```bash
task-master rules add [name]
# Any other setup steps
```

### Example Usage

<!-- Show it in action -->

```bash
# Real example
```

### Natural Language Hooks (if applicable)

```
"When tests pass, mark task as done"
# Other examples
```

## Contributor Checklist

- [ ] Created changeset: `npm run changeset`
- [ ] Tests pass: `npm test`
- [ ] Format check passes: `npm run format-check`
- [ ] Addressed CodeRabbit comments
- [ ] Integration fully tested with target tool/IDE
- [ ] Error scenarios tested
- [ ] Added integration tests
- [ ] Documentation includes setup guide
- [ ] Examples are working and clear

---

## For Maintainers

- [ ] Integration stability verified
- [ ] Documentation comprehensive
- [ ] Examples working
