# What type of PR is this?
<!-- Check one -->

 - [ ] 🐛 Bug fix
 - [ ] ✨ Feature
 - [ ] 🔌 Integration
 - [ ] 📝 Docs
 - [ ] 🧹 Refactor
 - [ ] Other:
## Description
<!-- What does this PR do? -->

## Related Issues
<!-- Link issues: Fixes #123 -->

## How to Test This
<!-- Quick steps to verify the changes work -->
```bash
# Example commands or steps
```

**Expected result:**
<!-- What should happen? -->

## Contributor Checklist

- [ ] Created changeset: `npm run changeset`
- [ ] Tests pass: `npm test`
- [ ] Format check passes: `npm run format-check` (or `npm run format` to fix)
- [ ] Addressed CodeRabbit comments (if any)
- [ ] Linked related issues (if any)
- [ ] Manually tested the changes

## Changelog Entry
<!-- One line describing the change for users -->
<!-- Example: "Added Kiro IDE integration with automatic task status updates" -->

---

### For Maintainers

- [ ] PR title follows conventional commits
- [ ] Target branch correct
- [ ] Labels added
- [ ] Milestone assigned (if applicable)
