import requests
import json
import time

# 定义API的URL
url = "http://localhost:5000/activate_features"

# 定义请求头
headers = {
    'Content-Type': 'application/json'
}

# 定义要激活的功能
data = {
    'search': True,  # 激活联网搜索
    'thinking': True  # 激活思考功能
}

# 发送POST请求
try:
    print("正在请求激活功能...")
    print(f"要激活的功能: {', '.join([k for k, v in data.items() if v])}")
    print("请稍等，这可能需要一些时间...")
    
    start_time = time.time()
    response = requests.post(
        url, 
        headers=headers,
        data=json.dumps(data),
        timeout=60  # 增加超时时间到1分钟
    )
    end_time = time.time()
    
    print(f"请求耗时: {end_time - start_time:.2f} 秒")
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        try:
            result = response.json()
            print(f"\n状态: {result.get('status', '未知')}")
            print(f"消息: {result.get('message', '无消息')}")
        except json.JSONDecodeError:
            print("响应不是有效的JSON格式")
            print(f"响应内容: {response.text[:200]}..." if len(response.text) > 200 else f"响应内容: {response.text}")
    else:
        print(f"错误: {response.status_code}")
        print(f"响应内容: {response.text[:200]}..." if len(response.text) > 200 else f"响应内容: {response.text}")
        
except Exception as e:
    print(f"发生错误: {str(e)}")
