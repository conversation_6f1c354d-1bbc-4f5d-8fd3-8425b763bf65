[project]
name = "ai_knowledge_bridge"
version = "0.1.0"
description = "MCP tool for AI knowledge sharing between less knowledgeable and more knowledgeable AIs"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "mcp>=1.7.1",
    "flask>=2.0.0",
    "requests>=2.0.0",
    "selenium>=4.0.0",
]

[[project.authors]]
name = "AI Knowledge Bridge"
email = "<EMAIL>"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project.scripts]
ai_knowledge_bridge = "ai_knowledge_bridge:main"
