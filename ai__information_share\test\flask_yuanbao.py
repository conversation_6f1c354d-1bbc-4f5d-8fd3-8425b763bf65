from flask import Flask, request, jsonify
from selenium import webdriver
from selenium.webdriver.edge.service import Service
from selenium.webdriver.edge.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time

# 初始化Flask应用
app = Flask(__name__)

# 配置Edge选项
edge_options = Options()
edge_options.add_argument("--window-size=1920,1080")

# 关键步骤：添加您的个人配置文件路径
# Edge配置文件通常位于这个路径
edge_options.add_argument("--user-data-dir=C:/Users/<USER>/AppData/Local/Microsoft/Edge/User Data")
# 如果有多个配置文件，可以指定使用哪一个
edge_options.add_argument("--profile-directory=Default")
# 初始化WebDriver
driver = webdriver.Chrome(options=edge_options)


# 初始化函数，访问第一个页面并打开目标页面
def initialize_chat_page():
    # 导航至初始页面
    driver.get("init")
    
    # 等待页面加载
    time.sleep(3)
    
    # 保存当前窗口句柄
    original_window = driver.current_window_handle
    
    # 点击打开新页面的链接（需要替换为实际选择器）
    link_selector = "你的链接选择器"  # 例如 ".nav-link" 或 "a[href='目标URL']"
    link_element = WebDriverWait(driver, 10).until(
        EC.element_to_be_clickable((By.CSS_SELECTOR, link_selector))
    )
    link_element.click()
    
    # 等待新窗口打开
    WebDriverWait(driver, 10).until(EC.number_of_windows_to_be(2))
    
    # 切换到新窗口
    for window_handle in driver.window_handles:
        if window_handle != original_window:
            driver.switch_to.window(window_handle)
            break
    
    # 等待新页面加载完成
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.TAG_NAME, "body"))
    )
    
    print(f"已切换到新页面: {driver.title}")

# 在启动应用前初始化聊天页面
initialize_chat_page()
'''
# API端点处理聊天请求
@app.route('/chat', methods=['POST'])
def chat():
    try:
        # 从POST请求中获取消息
        data = request.json
        message = data.get('content', '')
        
        # 找到消息输入字段
        input_selector = ".ql-editor[contenteditable='true']"
        
        input_element = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, input_selector))
        )
        
        # 清除现有文本并输入新消息
        input_element.clear()
        input_element.send_keys(message)
        
        # 找到并点击发送按钮
        send_button_selector = ".style__text-area__end___G64SV a"
        send_button = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, send_button_selector))
        )
        send_button.click()
        
        # 检测AI回答是否完成的选择器
        # 这是您提供的路径的CSS选择器版本
        ai_typing_indicator = ".style__text-area__end___G64SV > div:nth-child(2) > div > a > span"
        
        # 等待AI开始回复（指示器出现）- 可选步骤
        try:
            WebDriverWait(driver, 5).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ai_typing_indicator))
            )
        except:
            pass  # 如果没有出现指示器，继续执行
        
        # 等待AI完成回复（指示器消失）
        WebDriverWait(driver, 30).until(
            EC.invisibility_of_element_located((By.CSS_SELECTOR, ai_typing_indicator))
        )
        
        # 等待一个短暂的时间以确保响应已完全加载
        time.sleep(1)
        
        # 获取响应文本
        response_selector = ".agent-chat__list__item--ai.agent-chat__list__item--last .agent-chat__conv--ai__speech_show > div"
        response_element = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, response_selector))
        )
        response_text = response_element.text
        
        # 将响应作为JSON返回
        return jsonify({"response": response_text})
    
    except Exception as e:
        # 处理任何错误
        return jsonify({"error": str(e)}), 500

# 运行Flask应用
if __name__ == '__main__':
    app.run(host='127.0.0.1', port=5000, debug=False)
    # 当应用关闭时，退出浏览器
    driver.quit()
'''