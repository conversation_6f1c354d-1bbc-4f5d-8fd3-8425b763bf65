# Change Log

## 0.23.1

### Patch Changes

- [#1090](https://github.com/eyaltoledano/claude-task-master/pull/1090) [`a464e55`](https://github.com/eyaltoledano/claude-task-master/commit/a464e550b886ef81b09df80588fe5881bce83d93) Thanks [@Crunchyman-ralph](https://github.com/Crunchyman-ralph)! - Fix issues with some users not being able to connect to Taskmaster MCP server while using the extension

- Updated dependencies [[`4357af3`](https://github.com/eyaltoledano/claude-task-master/commit/4357af3f13859d90bca8795215e5d5f1d94abde5), [`e495b2b`](https://github.com/eyaltoledano/claude-task-master/commit/e495b2b55950ee54c7d0f1817d8530e28bd79c05), [`36468f3`](https://github.com/eyaltoledano/claude-task-master/commit/36468f3c93faf4035a5c442ccbc501077f3440f1), [`e495b2b`](https://github.com/eyaltoledano/claude-task-master/commit/e495b2b55950ee54c7d0f1817d8530e28bd79c05), [`e495b2b`](https://github.com/eyaltoledano/claude-task-master/commit/e495b2b55950ee54c7d0f1817d8530e28bd79c05), [`75c514c`](https://github.com/eyaltoledano/claude-task-master/commit/75c514cf5b2ca47f95c0ad7fa92654a4f2a6be4b), [`4bb6370`](https://github.com/eyaltoledano/claude-task-master/commit/4bb63706b80c28d1b2d782ba868a725326f916c7)]:
  - task-master-ai@0.24.0

## 0.23.1-rc.1

### Patch Changes

- Updated dependencies [[`75c514c`](https://github.com/eyaltoledano/claude-task-master/commit/75c514cf5b2ca47f95c0ad7fa92654a4f2a6be4b)]:
  - task-master-ai@0.24.0-rc.2

## 0.23.1-rc.0

### Patch Changes

- [#1090](https://github.com/eyaltoledano/claude-task-master/pull/1090) [`a464e55`](https://github.com/eyaltoledano/claude-task-master/commit/a464e550b886ef81b09df80588fe5881bce83d93) Thanks [@Crunchyman-ralph](https://github.com/Crunchyman-ralph)! - Fix issues with some users not being able to connect to Taskmaster MCP server while using the extension

- Updated dependencies [[`4357af3`](https://github.com/eyaltoledano/claude-task-master/commit/4357af3f13859d90bca8795215e5d5f1d94abde5), [`36468f3`](https://github.com/eyaltoledano/claude-task-master/commit/36468f3c93faf4035a5c442ccbc501077f3440f1), [`4bb6370`](https://github.com/eyaltoledano/claude-task-master/commit/4bb63706b80c28d1b2d782ba868a725326f916c7)]:
  - task-master-ai@0.24.0-rc.1

## 0.23.0

### Minor Changes

- [#1064](https://github.com/eyaltoledano/claude-task-master/pull/1064) [`b82d858`](https://github.com/eyaltoledano/claude-task-master/commit/b82d858f81a1e702ad59d84d5ae8a2ca84359a83) Thanks [@Crunchyman-ralph](https://github.com/Crunchyman-ralph)! - 🎉 **Introducing TaskMaster Extension!**

  We're thrilled to launch the first version of our Code extension, bringing the power of TaskMaster directly into your favorite code editor. While this is our initial release and we've kept things focused, it already packs powerful features to supercharge your development workflow.

  ## ✨ Key Features

  ### 📋 Visual Task Management
  - **Kanban Board View**: Visualize all your tasks in an intuitive board layout directly in VS Code
  - **Drag & Drop**: Easily change task status by dragging cards between columns
  - **Real-time Updates**: See changes instantly as you work through your project

  ### 🏷️ Multi-Context Support
  - **Tag Switching**: Seamlessly switch between different project contexts/tags
  - **Isolated Workflows**: Keep different features or experiments organized separately

  ### 🤖 AI-Powered Task Updates
  - **Smart Updates**: Use TaskMaster's AI capabilities to update tasks and subtasks
  - **Context-Aware**: Leverages your existing TaskMaster configuration and models

  ### 📊 Rich Task Information
  - **Complexity Scores**: See task complexity ratings at a glance
  - **Subtask Visualization**: Expand tasks to view and manage subtasks
  - **Dependency Graphs**: Understand task relationships and dependencies visually

  ### ⚙️ Configuration Management
  - **Visual Config Editor**: View and understand your `.taskmaster/config.json` settings
  - **Easy Access**: No more manual JSON editing for common configuration tasks

  ### 🚀 Quick Actions
  - **Status Updates**: Change task status with a single click
  - **Task Details**: Access full task information without leaving VS Code
  - **Integrated Commands**: All TaskMaster commands available through the command palette

  ## 🎯 What's Next?

  This is just the beginning! We wanted to get a solid foundation into your hands quickly. The extension will evolve rapidly with your feedback, adding more advanced features, better visualizations, and deeper integration with your development workflow.

  Thank you for being part of the TaskMaster journey. Your workflow has never looked better! 🚀

## 0.23.0-rc.1

### Minor Changes

- [#1064](https://github.com/eyaltoledano/claude-task-master/pull/1064) [`b82d858`](https://github.com/eyaltoledano/claude-task-master/commit/b82d858f81a1e702ad59d84d5ae8a2ca84359a83) Thanks [@Crunchyman-ralph](https://github.com/Crunchyman-ralph)! - 🎉 **Introducing TaskMaster Extension!**

  We're thrilled to launch the first version of our Code extension, bringing the power of TaskMaster directly into your favorite code editor. While this is our initial release and we've kept things focused, it already packs powerful features to supercharge your development workflow.

  ## ✨ Key Features

  ### 📋 Visual Task Management
  - **Kanban Board View**: Visualize all your tasks in an intuitive board layout directly in VS Code
  - **Drag & Drop**: Easily change task status by dragging cards between columns
  - **Real-time Updates**: See changes instantly as you work through your project

  ### 🏷️ Multi-Context Support
  - **Tag Switching**: Seamlessly switch between different project contexts/tags
  - **Isolated Workflows**: Keep different features or experiments organized separately

  ### 🤖 AI-Powered Task Updates
  - **Smart Updates**: Use TaskMaster's AI capabilities to update tasks and subtasks
  - **Context-Aware**: Leverages your existing TaskMaster configuration and models

  ### 📊 Rich Task Information
  - **Complexity Scores**: See task complexity ratings at a glance
  - **Subtask Visualization**: Expand tasks to view and manage subtasks
  - **Dependency Graphs**: Understand task relationships and dependencies visually

  ### ⚙️ Configuration Management
  - **Visual Config Editor**: View and understand your `.taskmaster/config.json` settings
  - **Easy Access**: No more manual JSON editing for common configuration tasks

  ### 🚀 Quick Actions
  - **Status Updates**: Change task status with a single click
  - **Task Details**: Access full task information without leaving VS Code
  - **Integrated Commands**: All TaskMaster commands available through the command palette

  ## 🎯 What's Next?

  This is just the beginning! We wanted to get a solid foundation into your hands quickly. The extension will evolve rapidly with your feedback, adding more advanced features, better visualizations, and deeper integration with your development workflow.

  Thank you for being part of the TaskMaster journey. Your workflow has never looked better! 🚀

## 0.23.0-rc.0

### Minor Changes

- [#997](https://github.com/eyaltoledano/claude-task-master/pull/997) [`64302dc`](https://github.com/eyaltoledano/claude-task-master/commit/64302dc1918f673fcdac05b29411bf76ffe93505) Thanks [@DavidMaliglowka](https://github.com/DavidMaliglowka)! - 🎉 **Introducing TaskMaster Extension!**

  We're thrilled to launch the first version of our Code extension, bringing the power of TaskMaster directly into your favorite code editor. While this is our initial release and we've kept things focused, it already packs powerful features to supercharge your development workflow.

  ## ✨ Key Features

  ### 📋 Visual Task Management
  - **Kanban Board View**: Visualize all your tasks in an intuitive board layout directly in VS Code
  - **Drag & Drop**: Easily change task status by dragging cards between columns
  - **Real-time Updates**: See changes instantly as you work through your project

  ### 🏷️ Multi-Context Support
  - **Tag Switching**: Seamlessly switch between different project contexts/tags
  - **Isolated Workflows**: Keep different features or experiments organized separately

  ### 🤖 AI-Powered Task Updates
  - **Smart Updates**: Use TaskMaster's AI capabilities to update tasks and subtasks
  - **Context-Aware**: Leverages your existing TaskMaster configuration and models

  ### 📊 Rich Task Information
  - **Complexity Scores**: See task complexity ratings at a glance
  - **Subtask Visualization**: Expand tasks to view and manage subtasks
  - **Dependency Graphs**: Understand task relationships and dependencies visually

  ### ⚙️ Configuration Management
  - **Visual Config Editor**: View and understand your `.taskmaster/config.json` settings
  - **Easy Access**: No more manual JSON editing for common configuration tasks

  ### 🚀 Quick Actions
  - **Status Updates**: Change task status with a single click
  - **Task Details**: Access full task information without leaving VS Code
  - **Integrated Commands**: All TaskMaster commands available through the command palette

  ## 🎯 What's Next?

  This is just the beginning! We wanted to get a solid foundation into your hands quickly. The extension will evolve rapidly with your feedback, adding more advanced features, better visualizations, and deeper integration with your development workflow.

  Thank you for being part of the TaskMaster journey. Your workflow has never looked better! 🚀
