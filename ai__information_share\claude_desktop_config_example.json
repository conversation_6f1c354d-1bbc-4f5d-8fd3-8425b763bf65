{"mcpServers": {"filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/Desktop", "/dev/MCP"]}, "mcp-server-fetch": {"runtime": "python", "command": "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Links/uvx", "args": ["mcp-server-fetch"]}, "blender": {"command": "uvx", "args": ["blender-mcp"]}, "@modelcontextprotocol-server-puppeteer": {"runtime": "node", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"]}, "auto-omni": {"runtime": "python", "command": "python", "args": ["c:/dev/MCP/AutoOmni/auto_omni_mcp.py"], "port": 5001}, "ai_knowledge_bridge": {"runtime": "python", "command": "python", "args": ["c:/dev/MCP/ai__information_share/run_ai_knowledge_bridge.py"]}}}