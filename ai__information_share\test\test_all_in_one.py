import requests
import json
import time

# 服务器基础URL
base_url = "http://localhost:5000"

# 定义请求头
headers = {
    'Content-Type': 'application/json'
}

def print_separator():
    print("\n" + "="*50 + "\n")

def call_api(endpoint, data=None, timeout=60):
    """调用API并返回结果"""
    url = f"{base_url}/{endpoint}"
    
    start_time = time.time()
    
    if data:
        response = requests.post(url, headers=headers, data=json.dumps(data), timeout=timeout)
    else:
        response = requests.post(url, headers=headers, timeout=timeout)
    
    end_time = time.time()
    
    print(f"请求耗时: {end_time - start_time:.2f} 秒")
    print(f"状态码: {response.status_code}")
    
    try:
        return response.status_code, response.json()
    except json.JSONDecodeError:
        return response.status_code, {"error": "无法解析JSON", "text": response.text}

# 主函数
def main():
    try:
        # 步骤1: 新建对话
        print("步骤1: 新建对话")
        print("正在请求新建对话...")
        status_code, result = call_api("new_conversation")
        
        if status_code == 200 and result.get("status") == "success":
            print(f"新建对话成功: {result.get('message', '')}")
        else:
            print(f"新建对话失败: {result}")
            return
        
        print_separator()
        
        # 步骤2: 激活功能
        print("步骤2: 激活功能")
        print("正在激活联网搜索和思考功能...")
        
        features_data = {
            'search': True,  # 激活联网搜索
            'thinking': True  # 激活思考功能
        }
        
        status_code, result = call_api("activate_features", features_data)
        
        if status_code == 200 and result.get("status") == "success":
            print(f"功能激活成功: {result.get('message', '')}")
        else:
            print(f"功能激活失败: {result}")
            return
        
        print_separator()
        
        # 步骤3: 发送消息
        print("步骤3: 发送消息")
        message = "你好，请介绍一下你自己，并解释一下量子计算的基本原理"
        print(f"正在发送消息: {message}")
        
        chat_data = {
            'content': message
        }
        
        status_code, result = call_api("chat", chat_data, timeout=180)  # 增加超时时间到3分钟
        
        if status_code == 200:
            print("\nAI回复:")
            print(result.get('response', '未收到回复'))
        else:
            print(f"发送消息失败: {result}")
            return
        
        print_separator()
        print("所有操作已成功完成!")
        
    except Exception as e:
        print(f"执行过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main()
