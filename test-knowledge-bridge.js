const DEFAULT_BASE_URL = 'http://127.0.0.1:5000';

async function testConnection() {
  try {
    console.log('Testing connection to:', DEFAULT_BASE_URL);
    
    // Test status endpoint
    const res = await fetch(`${DEFAULT_BASE_URL}/status`, { method: 'GET' });
    console.log('Response status:', res.status);
    console.log('Response ok:', res.ok);
    
    if (res.ok) {
      const data = await res.json();
      console.log('Response data:', data);
      console.log('Server ready:', !!data.ready);
    } else {
      console.log('Response not ok');
      const text = await res.text();
      console.log('Response text:', text);
    }
    
    // Test chat endpoint
    console.log('\nTesting chat endpoint...');
    const chatRes = await fetch(`${DEFAULT_BASE_URL}/chat`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ content: 'Hello, test message' })
    });
    
    console.log('Chat response status:', chatRes.status);
    if (chatRes.ok) {
      const chatData = await chatRes.json();
      console.log('Chat response:', chatData);
    } else {
      const chatText = await chatRes.text();
      console.log('Chat error:', chatText);
    }
    
  } catch (error) {
    console.error('Connection error:', error.message);
    console.error('Error details:', error);
  }
}

testConnection();
