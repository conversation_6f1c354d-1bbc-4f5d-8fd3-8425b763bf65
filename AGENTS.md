# Repository Guidelines

## Project Structure & Module Organization
- `claude-task-master/`: Node.js Task Master CLI + MCP server. Source in `src/`, executables in `bin/` and `mcp-server/`, docs in `docs/`, tests in `tests/`.
- `ai__information_share/`: Python utilities and demos (Flask MCP, knowledge bridge). Source in `src/`, examples in root, tests (if any) in `test/`.

## Build, Test, and Development Commands
Node (inside `claude-task-master/`):
- `npm ci`: Install dependencies.
- `npm test` | `npm run test:watch` | `npm run test:coverage`: Run Jest (watch/coverage variants).
- `npm run test:e2e`: Execute end‑to‑end tests.
- `npm run mcp-server`: Start the MCP server locally. Example: `npx -y --package=task-master-ai task-master-ai`.
- `node bin/task-master.js --help`: Run the CLI directly.

Python (inside `ai__information_share/`):
- `python -m venv .venv && . .venv/bin/activate`: Create a virtualenv.
- `pip install -e .`: Install package for local dev. Example demo: `python flask_aichat.py`.

## Coding Style & Naming Conventions
- JavaScript/Node: ESM (`"type": "module"`), 2‑space indent, semicolons optional. Use camelCase for variables/functions, PascalCase for classes, kebab‑case for filenames.
- Formatter/Linter: Biome via `npm run format-check` and `npm run format` (configured in `biome.json`). Keep imports sorted and avoid unused exports.
- Python: Follow PEP 8 (4‑space indent) and add type hints where practical.

## Testing Guidelines
- Framework: Jest (configured in `claude-task-master/jest.config.js`). Tests live under `claude-task-master/tests/{unit,integration,e2e}` and also `**/?(*.)+(spec|test).js`.
- Coverage: Target ≥80% global (thresholds configured). Use `npm run test:coverage` to generate `coverage/`.
- Test naming: Mirror source paths; prefer small, focused tests with clear arrange‑act‑assert.

## Commit & Pull Request Guidelines
- Branching: Open PRs against `next`. Use `feature/...` or `fix/...` branches.
- Commits: Prefer Conventional Commits (e.g., `feat:`, `fix:`, `docs:`). Keep messages concise and technical.
- Changesets: For user‑facing changes, run `npm run changeset` and commit the generated `.changeset/*.md`.
- PRs: Include a clear description, linked issues, test evidence (logs or screenshots), and note any config changes. Ensure `npm test` and `npm run format-check` pass.

## Security & Configuration Tips
- Secrets: Do not commit API keys. Copy `claude-task-master/.env.example` to `.env` and fill locally.
- MCP config: If using editors (Cursor/Windsurf/VS Code), define the MCP server per editor docs; keep credentials in env.
- Node version: Use Node 18+ (`.nvmrc` provided).

