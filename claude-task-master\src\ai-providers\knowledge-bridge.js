/**
 * knowledge-bridge.js
 * Provider that routes prompts to the local ai__information_share Flask server
 * instead of a remote LLM API. No API key required.
 */

import { BaseAIProvider } from './base-provider.js';
import { jsonrepair } from 'jsonrepair';
import { zodToJsonSchema } from 'zod-to-json-schema';
import { log } from '../../scripts/modules/utils.js';

const DEFAULT_BASE_URL = process.env.KNOWLEDGE_BRIDGE_URL || 'http://localhost:5000';

export class KnowledgeBridgeProvider extends BaseAIProvider {
  constructor() {
    super();
    this.name = 'KnowledgeBridge';
  }

  // No API key is required
  isRequiredApiKey() {
    return false;
  }

  getRequiredApiKeyName() {
    return null;
  }

  // Not used; we override generateText/streamText/generateObject
  getClient() {
    return null;
  }

  async _ensureServerReady() {
    try {
      const res = await fetch(`${DEFAULT_BASE_URL}/status`, { method: 'GET' });
      if (!res.ok) return false;
      const data = await res.json().catch(() => ({}));
      return !!data.ready;
    } catch {
      return false;
    }
  }

  _messagesToPrompt({ messages, systemPrompt }) {
    const parts = [];
    if (systemPrompt) parts.push(`[system]\n${systemPrompt}`);
    for (const m of messages || []) {
      parts.push(`[${m.role}]\n${typeof m.content === 'string' ? m.content : JSON.stringify(m.content)}`);
    }
    return parts.join('\n\n');
  }

  async _postChat(content, { timeoutMs = 180000 } = {}) {
    const controller = new AbortController();
    const t = setTimeout(() => controller.abort(), timeoutMs);
    try {
      const res = await fetch(`${DEFAULT_BASE_URL}/chat`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ content }),
        signal: controller.signal
      });
      const json = await res.json().catch(async () => ({ text: await res.text() }));
      if (!res.ok) {
        throw new Error(`Bridge error ${res.status}: ${JSON.stringify(json)}`);
      }
      const text = json.response || json.text || '';
      return text;
    } finally {
      clearTimeout(t);
    }
  }

  async generateText(params) {
    try {
      // Minimal validation specific to bridge
      if (!params || !Array.isArray(params.messages) || params.messages.length === 0) {
        throw new Error('Invalid or empty messages array provided');
      }

      const ready = await this._ensureServerReady();
      if (!ready) {
        throw new Error('Knowledge Bridge server is not ready at /status');
      }

      const prompt = this._messagesToPrompt({ messages: params.messages, systemPrompt: params.systemPrompt });
      const text = await this._postChat(prompt, { timeoutMs: 180000 });

      return {
        text,
        usage: { inputTokens: 0, outputTokens: 0, totalTokens: 0 }
      };
    } catch (error) {
      this.handleError('text generation', error);
    }
  }

  async streamText(params) {
    // Streaming not supported; fall back to non-stream call
    const result = await this.generateText(params);
    return {
      // Minimal stream-like interface: consumers using unified service treat streams specially;
      // since streamTextService isn’t referenced elsewhere, return the text for compatibility.
      toReadableStream: () => null,
      text: result?.text || ''
    };
  }

  async generateObject(params) {
    try {
      if (!params || !Array.isArray(params.messages) || params.messages.length === 0) {
        throw new Error('Invalid or empty messages array provided');
      }
      if (!params.schema) {
        throw new Error('Schema is required for object generation');
      }

      const ready = await this._ensureServerReady();
      if (!ready) {
        throw new Error('Knowledge Bridge server is not ready at /status');
      }

      // Convert Zod schema to JSON Schema for the instruction
      let schemaJson = {};
      try {
        schemaJson = zodToJsonSchema(params.schema, params.objectName || 'generated_object');
      } catch (e) {
        log('warn', `Failed to convert Zod schema to JSON Schema: ${e.message}`);
      }

      const guidance = [
        'You must output ONLY valid JSON for the requested object.',
        'Do not include explanations or markdown code fences.',
        params.objectName ? `Return an object named "${params.objectName}" (shape only).` : undefined,
        Object.keys(schemaJson || {}).length ? `The JSON must satisfy this JSON Schema: ${JSON.stringify(schemaJson)}` : undefined
      ].filter(Boolean).join('\n');

      const bridgedMessages = [
        ...(params.systemPrompt ? [{ role: 'system', content: params.systemPrompt }] : []),
        { role: 'system', content: guidance },
        ...params.messages
      ];

      const prompt = this._messagesToPrompt({ messages: bridgedMessages });
      const raw = await this._postChat(prompt, { timeoutMs: 240000 });

      // Strip code fences if present
      const cleaned = raw
        .replace(/^```(json)?/i, '')
        .replace(/```\s*$/i, '')
        .trim();

      let parsed;
      try {
        parsed = JSON.parse(cleaned);
      } catch (e) {
        const repaired = jsonrepair(cleaned);
        parsed = JSON.parse(repaired);
      }

      return {
        object: parsed,
        usage: { inputTokens: 0, outputTokens: 0, totalTokens: 0 }
      };
    } catch (error) {
      this.handleError('object generation', error);
    }
  }
}

