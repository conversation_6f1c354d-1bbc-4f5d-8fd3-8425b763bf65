# AI Knowledge Bridge (AI信息共享)

MCP tool for AI knowledge sharing between less knowledgeable and more knowledgeable AIs. The less knowledgeable AI forwards user questions to a more knowledgeable AI and returns the responses to users.

## 功能特点

- 自动化AI间的信息传递
- 利用知识更丰富的AI来增强AI应答
- 支持创建新对话
- 支持激活联网搜索和思考功能
- 简单易用的MCP接口

## 组件

### 资源

服务器实现了一个简单的对话存储系统：
- 自定义conversation://URI方案用于访问对话
- 每个对话资源都有名称、描述和text/plain的MIME类型

### 提示

服务器提供单一提示：
- enhanced-question：生成增强的问题
  - 可选的"search"参数控制是否启用联网搜索
  - 可选的"thinking"参数控制是否启用思考功能
  - 生成结合用户问题和功能偏好的提示

### 工具

服务器实现了三个工具：
- start-conversation：开始新的对话
- send-message：向聪明AI发送消息
- activate-features：激活特定功能（联网搜索和思考）

## 配置

### Claude Desktop

在MacOS: `~/Library/Application\ Support/Claude/claude_desktop_config.json`
在Windows: `%APPDATA%/Claude/claude_desktop_config.json`

<details>
  <summary>开发/未发布服务器配置</summary>

  ```json
  "mcpServers": {
    "ai_knowledge_bridge": {
      "runtime": "python",
      "command": "python",
      "args": [
        "-m",
        "ai_knowledge_bridge"
      ]
    }
  }
  ```
</details>

<details>
  <summary>已发布服务器配置</summary>

  ```json
  "mcpServers": {
    "ai_knowledge_bridge": {
      "runtime": "python",
      "command": "python",
      "args": [
        "-m",
        "ai_knowledge_bridge"
      ]
    }
  }
  ```
</details>

## 开发

### 构建和发布

准备分发包：

1. 同步依赖并更新锁文件：
```bash
uv sync
```

2. 构建包分发：
```bash
uv build
```

这将在`dist/`目录中创建源代码和wheel分发。

3. 发布到PyPI：
```bash
uv publish
```

注意：您需要通过环境变量或命令标志设置PyPI凭据：
- Token: `--token` 或 `UV_PUBLISH_TOKEN`
- 或用户名/密码: `--username`/`UV_PUBLISH_USERNAME` 和 `--password`/`UV_PUBLISH_PASSWORD`

### 调试

由于MCP服务器通过stdio运行，调试可能具有挑战性。为获得最佳调试体验，我们强烈推荐使用[MCP Inspector](https://github.com/modelcontextprotocol/inspector)。

您可以通过[`npm`](https://docs.npmjs.com/downloading-and-installing-node-js-and-npm)使用以下命令启动MCP Inspector：

```bash
npx @modelcontextprotocol/inspector python -m ai_knowledge_bridge
```

启动后，Inspector将显示一个URL，您可以在浏览器中访问该URL开始调试。
