# Dependency directories
node_modules/
jspm_packages/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Cursor configuration -- might have ENV variables. Included by default
# .cursor/mcp.json

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Jest cache
.jest/

# Test temporary files and directories
tests/temp/
tests/e2e/_runs/
tests/e2e/log/
tests/**/*.log
tests/**/coverage/

# Test database files (if any)
tests/**/*.db
tests/**/*.sqlite
tests/**/*.sqlite3

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test

# parcel-bundler cache
.cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Mac files
.DS_Store

# Debug files
*.debug
init-debug.log
dev-debug.log

# NPMRC
.npmrc

# Added by Task Master AI
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# VS Code extension test files
.vscode-test/
apps/extension/.vscode-test/

# apps/extension
apps/extension/vsix-build/