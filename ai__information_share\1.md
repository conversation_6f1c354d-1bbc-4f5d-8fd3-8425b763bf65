**3.4.1 四足机器人**

**1. 四足机器人腿足机械结构**

四足机器人行走时需要控制每条腿到达期望的落脚点。考虑到系统的复杂性以及自重等因素，通常每条机械腿设置3个自由度即可满足行走需求。一种常见的设计方案是借鉴工业机械臂，采用旋转关节串联刚性杆构成机械腿，如图3-40a所示。机器人的躯干相对于机械臂的固定基座，用于连接四条机械腿的髋关节；机械腿髋部通常包含两个正交旋转关节，膝部包含一个旋转关节，整合四足机器人需要至少12个自由度。由于3自由度机械腿只能控制足端的空间位置，无法控制其姿态，因此理论上足与地面之间的接触方式应为点接触，通过步态时序的配合，利用地面的摩擦反作用力作为移动的驱动力实现前进、后退和转向。

**2. 四足机器人腿部正运动学**

四足机器人腿部正运动学建模指的是把机器人的躯干作为固定基座，求解机器人足端空间位置的过程。首先，建立描述机器人和腿部关节运动的坐标系，包括：地面上的全局坐标系 $O_{xyz}$，描述机器人躯干位姿 $R_0$ 的坐标系 $O_0x_0y_0z_0$，位于机械腿三个关节即描述关节相对转动姿态 $R_i$ 的关节坐标系 $O_1x_1y_1z_1$、$O_2x_2y_2z_2$ 以及 $O_3x_3y_3z_3$。初始状态下，机器人的关节坐标系各坐标轴均与机器人躯干坐标系分别对应平行，旋转轴矢量作为关节轴矢量 $a_i$；各关节的位置可以用坐标系之间的相对位置矢量 $r_i$ 确定，如图3-40a所示。关节运动的正运动学可以用于下的齐次变换矩阵建模：

(图3-40 典型四足机器人的机械腿结构 a) 机械腿机构 b) 机械腿坐标系)

根据图3-40a可知，三个关节轴矢量分别为：$a_1 = \begin{pmatrix} 0 \\ 0 \\ 1 \end{pmatrix}$，$a_2 = \begin{pmatrix} 1 \\ 0 \\ 0 \end{pmatrix}$，$a_3 = \begin{pmatrix} 1 \\ 0 \\ 0 \end{pmatrix}$，三个相对位置矢量分别为：$r_1 = \begin{pmatrix} 0 \\ b_1 \\ 0 \end{pmatrix}$，$r_2 = 0$，$r_3 = \begin{pmatrix} 0 \\ 0 \\ -b_3 \end{pmatrix}$。为简化表达，记 $\cos\theta_i \rightarrow c_i$，$\sin\theta_i \rightarrow s_i$，$\cos(\theta_i + \theta_j) \rightarrow c_{ij}$，$\sin(\theta_i + \theta_j) \rightarrow s_{ij}$，根据链式法则，机器人足端的空间位姿可由如下方程获得：

$T_4^0 = T_1^0 T_2^1 T_3^2 T_4^3$
$= \begin{pmatrix} R_1^0 & r_1 \\ 0 & 1 \end{pmatrix} \begin{pmatrix} R_2^1 & r_2 \\ 0 & 1 \end{pmatrix} \begin{pmatrix} R_3^2 & r_3 \\ 0 & 1 \end{pmatrix} \begin{pmatrix} I & r_4 \\ 0 & 1 \end{pmatrix}$
$= \begin{pmatrix} 1 & 0 & 0 & 0 \\ 0 & c_1 & -s_1 & b_1 \\ 0 & s_1 & c_1 & 0 \\ 0 & 0 & 0 & 1 \end{pmatrix} \begin{pmatrix} c_2 & 0 & s_2 & 0 \\ 0 & 1 & 0 & 0 \\ -s_2 & 0 & c_2 & 0 \\ 0 & 0 & 0 & 1 \end{pmatrix} \begin{pmatrix} c_3 & 0 & s_3 & 0 \\ 0 & 1 & 0 & 0 \\ -s_3 & 0 & c_3 & -b_3 \\ 0 & 0 & 0 & 1 \end{pmatrix} \begin{pmatrix} 1 & 0 & 0 & 0 \\ 0 & 1 & 0 & 0 \\ 0 & 0 & 1 & -b_4 \\ 0 & 0 & 0 & 1 \end{pmatrix}$ (3-57)
$= \begin{pmatrix} c_{23} & 0 & s_{23} & -s_{23}b_4 - s_2b_3 \\ s_1s_{23} & c_1 & -s_1c_{23} & s_1c_{23}b_4 + s_1s_2b_3 + b_1 \\ -c_1s_{23} & -s_1 & c_1c_{23} & -c_1c_{23}b_4 - c_1s_2b_3 \\ 0 & 0 & 0 & 1 \end{pmatrix}$

由上式可知，四足机器人的3自由度单腿足端空间位置的正运动学解为
$ \begin{pmatrix} p_x \\ p_y \\ p_z \end{pmatrix} = \begin{pmatrix} -s_{23}b_4 - s_2b_3 \\ s_1c_{23}b_4 + s_1s_2b_3 + b_1 \\ -c_1c_{23}b_4 - c_1s_2b_3 \end{pmatrix} $

**3. 四足机器人腿部逆运动学**

根据对四足机器人足端运动规划的结果，可以获得足端绝对位姿，即
$T_4^0 = \begin{pmatrix} n_x & o_x & a_x & p_x \\ n_y & o_y & a_y & p_y \\ n_z & o_z & a_z & p_z \\ 0 & 0 & 0 & 1 \end{pmatrix}$

将上式与式 (3-57) 的结果比对，利用矩阵元素 $(2,2)$ 和 $(3,2)$ 项对应相等，即
$c_1 = o_y$
$s_1 = o_z$
利用双变量反正切函数，可以求得
$\theta_1 = \text{atan2}(o_z, o_y)$ (3-58)

同理，利用如下对应关系
$c_{23} = n_x$
$s_{23} = a_x$

可知
$p_x = -s_{23}b_4 - s_2b_3$
$p_y = s_1c_{23}b_4 + s_1s_2b_3 + b_1$

$\theta_2 = \text{atan2} \left( \frac{a_1b_4 + p_x}{b_3}, \frac{p_y - o_y b_4 - b_1}{o_z b_3} \right)$ (3-59)
$\theta_3 = \text{atan2}(a_x, n_x) - \theta_2$ (3-60)

**常数项忽略原因**： - $p(z)$ 作为归一化常数与 $x$ 无关，优化时可省略。 - 仅需比较不同 $x$ 对应的相对概率，常数项不影响极值点。 