from flask import Flask, request, jsonify
from selenium import webdriver
from selenium.webdriver.edge.service import Service
from selenium.webdriver.edge.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import atexit
import os
import random
import socket
import sys
import requests
from pathlib import Path
from selenium.webdriver.edge.service import Service as EdgeService


# 检查端口是否被占用的函数
def is_port_in_use(port):
    """检查指定端口是否被占用"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        try:
            s.bind(('localhost', port))
            return False
        except socket.error:
            return True

def check_server_running(port=5000):
    """检查服务器是否已经在运行"""
    if is_port_in_use(port):
        try:
            # 尝试访问状态端点
            response = requests.get(f"http://localhost:{port}/status", timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get("ready", False):
                    print(f"检测到服务器已在端口 {port} 运行且状态正常")
                    return True
        except Exception as e:
            print(f"端口 {port} 被占用，但无法确认是否为本服务: {e}")
            return True
    return False

# 初始化Flask应用
app = Flask(__name__)

# 服务器状态标志
server_ready = False

# 创建一个专用于自动化的Edge配置文件路径
automation_profile_dir = os.path.join(os.path.expanduser("~"), "EdgeAutomation")
os.makedirs(automation_profile_dir, exist_ok=True)

# 配置Edge选项
edge_options = Options()
edge_options.add_argument("--window-size=1920,1080")

# 使用独立的用户配置文件，而不是用户当前使用的配置文件
edge_options.add_argument(f"--user-data-dir={automation_profile_dir}")
edge_options.add_argument("--profile-directory=Default")

# 添加隐藏自动化特征的设置
edge_options.add_argument("--disable-blink-features=AutomationControlled")
edge_options.add_experimental_option("excludeSwitches", ["enable-automation"])
edge_options.add_experimental_option("useAutomationExtension", False)

# 设置detach=True，使浏览器在脚本结束后不关闭
edge_options.add_experimental_option("detach", True)

# 初始化WebDriver - 使用更健壮的方法
try:
    print("尝试初始化Edge WebDriver...")
    # 尝试使用Edge WebDriver
    edge_service = EdgeService(service_args=['--disable-build-check=true'])
    edge_service.path = r'C:/Users/<USER>/.cache/selenium/msedgedriver/win64/139.0.3405.102/msedgedriver.exe'
    driver = webdriver.Edge(options=edge_options,service=edge_service)
    print("Edge WebDriver初始化成功")
except Exception as edge_error:
    print(f"Edge WebDriver初始化失败: {edge_error}")
    print("尝试使用Chrome WebDriver作为备选...")

    # 如果Edge失败，尝试使用Chrome
    try:
        from selenium.webdriver.chrome.options import Options as ChromeOptions
        chrome_options = ChromeOptions()
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option("useAutomationExtension", False)
        chrome_options.add_experimental_option("detach", True)

        # 创建Chrome用户配置文件路径
        chrome_profile_dir = os.path.join(os.path.expanduser("~"), "ChromeAutomation")
        os.makedirs(chrome_profile_dir, exist_ok=True)
        chrome_options.add_argument(f"--user-data-dir={chrome_profile_dir}")

        driver = webdriver.Chrome(options=chrome_options)
        print("Chrome WebDriver初始化成功")
    except Exception as chrome_error:
        print(f"Chrome WebDriver初始化也失败: {chrome_error}")
        raise Exception("无法初始化任何WebDriver，请确保已安装Edge或Chrome浏览器")

# 执行JavaScript来伪装WebDriver特征
stealth_js = """
Object.defineProperty(navigator, 'webdriver', {
    get: () => false,
});
"""
driver.execute_script(stealth_js)

# 全局变量，用于保存聊天窗口的句柄
chat_window_handle = None

# 新建对话函数
def new_conversation():
    """
    使用新的XPath选择器点击"新建对话"按钮
    """
    try:
        print("尝试新建对话...")
        # 确保我们在正确的聊天窗口中
        global chat_window_handle
        if chat_window_handle and driver.current_window_handle != chat_window_handle:
            print(f"当前窗口句柄: {driver.current_window_handle}，需要切换到聊天窗口: {chat_window_handle}")
            driver.switch_to.window(chat_window_handle)
            print(f"已切换到聊天窗口，当前URL: {driver.current_url}")

        # 使用新的XPath选择器
        new_chat_xpath = "/html/body/div[2]/div[1]/nav/div[2]/div[1]/div[1]/div/a"
        print(f"使用新的XPath选择器: {new_chat_xpath}")

        try:
            new_chat_button = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, new_chat_xpath))
            )
            print("使用XPath选择器找到新建对话按钮")
        except Exception as xpath_error:
            print(f"使用XPath选择器查找新建对话按钮失败: {xpath_error}")
            # 使用备用的CSS选择器
            print("尝试使用备用的CSS选择器...")
            new_chat_selector = "body > div.flex.min-h-screen.w-full.overflow-x-clip > div.fixed.z-sidebar.lg/:sticky > nav > div.flex.flex-col.align-center.h-full.overflow-hidden.transition-all.duration-200 > div.flex.flex-col.px-2.pt-1.gap-px.mb-6 > div.mb-1 > div > a > div > div.transition-all.duration-200.text-accent-main-100.font-medium.text-sm"
            new_chat_button = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, new_chat_selector))
            )
            print("使用备用CSS选择器找到新建对话按钮")

        human_like_click(new_chat_button)
        print("已点击新建对话按钮")

        # 等待页面加载
        human_like_delay()
        print("新对话已创建")
        return True

    except Exception as e:
        print(f"新建对话失败: {e}")
        return False

# 功能激活函数
def activate_features(search=True, thinking=True):
    """
    使用新的XPath选择器激活功能

    参数:
    search (bool): 是否激活联网搜索功能
    thinking (bool): 是否激活思考功能
    """
    try:
        print("尝试激活功能...")
        # 确保我们在正确的聊天窗口中
        global chat_window_handle
        if chat_window_handle and driver.current_window_handle != chat_window_handle:
            print(f"当前窗口句柄: {driver.current_window_handle}，需要切换到聊天窗口: {chat_window_handle}")
            driver.switch_to.window(chat_window_handle)
            print(f"已切换到聊天窗口，当前URL: {driver.current_url}")

        # 首先点击功能菜单触发器
        menu_trigger_selector = "button[data-testid='input-menu-tools']"
        print(f"尝试使用ID选择器查找功能菜单触发器: {menu_trigger_selector}")

        try:
            # 等待元素变为可点击状态
            menu_trigger = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, menu_trigger_selector))
            )
            print("成功找到功能菜单触发器！")

            # 在这里可以继续执行点击等操作
            # menu_trigger.click()

        except Exception as e:
            print(f"使用ID选择器 '{menu_trigger_selector}' 查找功能菜单触发器失败: {e}")

        human_like_click(menu_trigger)
        print("已点击功能菜单触发器")
        human_like_delay()

        # 如果需要激活联网搜索
        if search:
            search_toggle_selector = "div[class='flex flex-col'] > div:not([data-state='closed']) input[role='switch']"
            print(f"使用新的联网搜索开关选择器: {search_toggle_selector}")

            try:
                search_toggle = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, search_toggle_selector))
                )
                print("使用CSS选择器找到联网搜索开关")
            except Exception as css_error:
                print(f"使用CSS选择器查找联网搜索开关失败: {css_error}")
                # 使用备用的XPath选择器
                print("尝试使用备用的XPath选择器...")
                search_toggle_xpath = "/html/body/div[2]/div[2]/div/div[1]/div/div/div[2]/fieldset/div[2]/div[1]/div[2]/div[1]/div[2]/div/div[2]/div/div/div/div/div[1]/div[4]/button/div[3]/div[1]"
                search_toggle = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, search_toggle_xpath))
                )
                print("使用备用XPath选择器找到联网搜索开关")

            human_like_click(search_toggle)
            print("已点击联网搜索开关")
            human_like_delay()

        # 如果需要激活思考功能
        if thinking:
            thinking_toggle_selector = "div[class='flex flex-col'] > div[data-state='closed'] input[role='switch']"
            print(f"使用新的思考功能开关选择器: {thinking_toggle_selector}")

            try:
                thinking_toggle = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, thinking_toggle_selector))
                )
                print("使用CSS选择器找到思考功能开关")
            except Exception as css_error:
                print(f"使用CSS选择器查找思考功能开关失败: {css_error}")
                # 使用备用的XPath选择器
                print("尝试使用备用的XPath选择器...")
                thinking_toggle_xpath = '/html/body/div[2]/div[2]/div/div[1]/div/div/div[2]/fieldset/div[2]/div[1]/div[2]/div[1]/div[2]/div/div[2]/div/div/div/div/div[1]/div[4]/button/div[3]/div[1]'
                thinking_toggle = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, thinking_toggle_xpath))
                )
                print("使用备用XPath选择器找到思考功能开关")

            human_like_click(thinking_toggle)
            print("已点击思考功能开关")
            human_like_delay()

        # 点击其他地方关闭菜单
        try:
            body_element = driver.find_element(By.TAG_NAME, "body")
            human_like_click(body_element)
            print("已关闭功能菜单")
        except Exception as close_error:
            print(f"关闭功能菜单失败: {close_error}")

        return True

    except Exception as e:
        print(f"激活功能失败: {e}")
        return False

# 注册脚本退出时的处理函数，防止quit()被调用
def quit_handler():
    # 不执行driver.quit()，这样浏览器会保持开启
    print("脚本退出，浏览器保持开启")

# 注册退出处理函数
atexit.register(quit_handler)

# 初始网站URL - 已设置为实际的AI对话网站URL
initial_url = "https://rawchat.cn/claude2.html"  # 使用rawchat.cn上的Claude AI接口

# 模拟真实用户行为的函数
def human_like_delay():
    """添加随机延迟，模拟人类操作"""
    time.sleep(random.uniform(0.5, 2.0))

def human_like_click(element):
    """模拟人类点击行为"""
    from selenium.webdriver.common.action_chains import ActionChains

    # 随机移动偏移量
    offset_x = random.randint(-10, 10)
    offset_y = random.randint(-10, 10)

    actions = ActionChains(driver)
    # 先移动到元素附近
    actions.move_to_element_with_offset(element, offset_x, offset_y)
    human_like_delay()
    # 然后移动到元素上
    actions.move_to_element(element)
    human_like_delay()
    # 最后点击
    actions.click()
    actions.perform()

# 获取图像尺寸的函数
def get_image_dimensions(img_element):
    try:
        # 先尝试从属性获取，这种方式更自然，不容易被检测
        width = img_element.get_attribute("width")
        height = img_element.get_attribute("height")

        # 如果无法从属性获取，再通过JavaScript获取
        if not width or not height:
            # 使用更自然的方式获取图像尺寸
            script = """
                var img = arguments[0];
                var width = img.width;
                var height = img.height;
                return {width: width, height: height};
            """
            result = driver.execute_script(script, img_element)
            width = result.get('width', 0)
            height = result.get('height', 0)

        # 转换为整数
        try:
            width = int(width) if width else 0
            height = int(height) if height else 0
        except (ValueError, TypeError):
            width = 0
            height = 0

        print(f"图像尺寸: {width}x{height}")
        return width, height
    except Exception as e:
        print(f"获取图像尺寸失败: {e}")
        return 0, 0

# 检测Cloudflare验证码并等待人工解决
def check_and_wait_for_cloudflare():
    """检测Cloudflare的人机验证并等待人工解决"""
    try:
        # 尝试检测Cloudflare验证页面的典型元素
        cloudflare_elements = [
            "iframe[src*='challenges.cloudflare.com']",  # Cloudflare iframe
            ".cf-browser-verification",  # 浏览器验证消息
            ".cf-error-code",  # 错误代码
            "#challenge-form",  # 挑战表单
            "h1[data-translate='challenge_headline']",  # 挑战标题
            ".ray-id"  # Ray ID
        ]

        for selector in cloudflare_elements:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    print(f"检测到Cloudflare人机验证 ({selector})")

                    # 给用户一个明确的提示
                    print("/n" + "="*50)
                    print("检测到Cloudflare验证码！请在浏览器中完成人机验证。")
                    print("完成验证后脚本将自动继续。")
                    print("="*50 + "/n")

                    # 循环等待，直到验证完成
                    while True:
                        # 检查是否所有验证元素都已消失
                        still_present = False
                        for sel in cloudflare_elements:
                            if driver.find_elements(By.CSS_SELECTOR, sel):
                                still_present = True
                                break

                        if not still_present:
                            print("验证已完成，继续执行...")
                            human_like_delay()  # 额外等待，确保页面完全加载
                            return True

                        # 等待几秒钟再次检查
                        time.sleep(3)

                    return True
            except Exception:
                continue

        return False  # 未检测到验证码
    except Exception as e:
        print(f"检测Cloudflare验证时出错: {e}")
        return False

# 初始化函数，访问第一个页面并处理可能出现的登录页面
def initialize_chat_page():
    # 导航至初始页面
    driver.get(initial_url)

    # 等待页面加载
    human_like_delay()

    # 检查是否出现登录页面
    try:
        # 设置较短的超时时间来检查登录按钮是否存在
        login_button = WebDriverWait(driver, 5).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR,
                "body > div > main > section > div > div > div > form > div.cc336b8c1 > button"))
        )
        print("检测到登录页面，点击登录按钮")
        human_like_click(login_button)

        # 等待登录后页面加载完成
        human_like_delay()

        # 检查当前URL是否已经改变，如果改变了，则重新导航回初始页面
        current_url = driver.current_url
        if current_url != initial_url:
            print(f"登录后URL已变更为: {current_url}，正在重新导航回初始页面")
            driver.get(initial_url)
            # 等待初始页面重新加载
            human_like_delay()

    except Exception as e:
        print(f"未检测到登录页面或登录页面处理出错: {e}")

    # 点击SweetAlert2确认按钮
    try:
        sweet_alert_confirm = WebDriverWait(driver, 5).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR,
                "body > div.swal2-container.swal2-center.swal2-backdrop-show > div > div.swal2-actions > button.swal2-confirm.swal2-styled"))
        )
        print("检测到SweetAlert2弹窗，点击确认按钮")
        human_like_click(sweet_alert_confirm)
        human_like_delay()
    except Exception as e:
        print(f"未检测到SweetAlert2弹窗或处理出错: {e}")

    # 图像尺寸检测处理
    found_target_size = False

    # 获取所有li元素
    li_elements = driver.find_elements(By.CSS_SELECTOR, "body > div.container > div.account-list > ul > li")

    # 保存当前窗口句柄，用于后续切换窗口
    original_window = driver.current_window_handle

    for i in range(min(18, len(li_elements))):
        if found_target_size:
            break

        li_index = i + 1  # CSS选择器从1开始计数

        try:
            # 检查该li元素是否包含class为plus的a标签
            a_elements = driver.find_elements(By.CSS_SELECTOR,
                f"body > div.container > div.account-list > ul > li:nth-child({li_index}) > a.plus")

            # 如果不存在class为plus的a标签，则跳过此li
            if not a_elements:
                print(f"li:nth-child({li_index}) 不包含class为plus的a标签，跳过")
                continue

            print(f"处理 li:nth-child({li_index})")

            # 获取图片元素
            try:
                img_element = driver.find_element(By.CSS_SELECTOR,
                    f"body > div.container > div.account-list > ul > li:nth-child({li_index}) > img")

                # 获取图片尺寸
                width, height = get_image_dimensions(img_element)

                # 检查是否是目标尺寸
                if width == 115 and height == 20:
                    found_target_size = True
                    print(f"找到目标尺寸图片(115x20)，点击 li:nth-child({li_index}) > a")
                    a_element = driver.find_element(By.CSS_SELECTOR,
                        f"body > div.container > div.account-list > ul > li:nth-child({li_index}) > a")

                    # 点击前记录当前窗口数量
                    window_count_before = len(driver.window_handles)

                    # 点击链接
                    human_like_click(a_element)
                    human_like_delay()

                    # 等待新窗口打开
                    try:
                        WebDriverWait(driver, 10).until(
                            lambda d: len(d.window_handles) > window_count_before
                        )
                        print("检测到新窗口已打开")

                        # 切换到新窗口
                        for window_handle in driver.window_handles:
                            if window_handle != original_window:
                                driver.switch_to.window(window_handle)
                                break

                        print(f"已切换到新窗口: {driver.title}")

                        # 检查是否出现Cloudflare验证
                        check_and_wait_for_cloudflare()

                        # 窗口切换后等待页面加载完成
                        WebDriverWait(driver, 10).until(
                            EC.presence_of_element_located((By.TAG_NAME, "body"))
                        )

                        # 保存聊天窗口句柄
                        global chat_window_handle
                        chat_window_handle = driver.current_window_handle
                        print(f"聊天界面初始化完成！当前窗口句柄: {chat_window_handle}")

                    except Exception as e:
                        print(f"等待新窗口或切换窗口时出错: {e}")
                        found_target_size = False

                    break
            except Exception as e:
                print(f"获取 li:nth-child({li_index}) 的图片元素时出错: {e}")

        except Exception as e:
            print(f"处理 li:nth-child({li_index}) 时出错: {e}")

    if not found_target_size:
        print("未找到目标尺寸(115x20)的图片，聊天界面初始化未完成")

# 在启动应用前初始化聊天页面
initialize_chat_page()

# 设置服务器已准备好
server_ready = True

# 添加路由处理
@app.route('/', methods=['GET'])
def home():
    return "自动化服务正在运行"

# 状态检查API端点
@app.route('/status', methods=['GET'])
def status():
    # 确保返回格式与ai_knowledge_bridge/server.py中的检查一致
    print("收到状态检查请求，返回ready=True")
    return jsonify({"ready": True})

# 新建对话API端点
@app.route('/new_conversation', methods=['POST'])
def new_conversation_api():
    try:
        result = new_conversation()
        if result:
            return jsonify({"status": "success", "message": "新对话已创建"})
        else:
            return jsonify({"status": "error", "message": "新建对话失败"}), 500
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

# 功能激活API端点
@app.route('/activate_features', methods=['POST'])
def activate_features_api():
    try:
        data = request.json or {}
        search = data.get('search', True)
        thinking = data.get('thinking', True)

        result = activate_features(search=search, thinking=thinking)
        if result:
            features = []
            if search:
                features.append("联网搜索")
            if thinking:
                features.append("思考")

            return jsonify({
                "status": "success",
                "message": f"功能已激活: {', '.join(features)}"
            })
        else:
            return jsonify({"status": "error", "message": "功能激活失败"}), 500
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500


# API端点处理聊天请求
@app.route('/chat', methods=['POST'])
def chat():
    try:
        # 从POST请求中获取消息
        data = request.json
        message = data.get('content', '')
        print(f"收到消息: {message}")

        global chat_window_handle
        
        # --- 新增的健壮窗口切换逻辑 ---
        found_chat_window = False
        
        # 1. 优先尝试使用缓存的句柄，这是最快的方式
        if chat_window_handle in driver.window_handles:
            try:
                driver.switch_to.window(chat_window_handle)
                # 切换后必须验证URL，防止页面已跳转
                if "claude4.ai1.bar" in driver.current_url.lower() :
                    print(f"成功切换到缓存的聊天窗口: {driver.current_url}")
                    found_chat_window = True
                else:
                    print("缓存的窗口句柄指向了错误的页面，将重新搜索。")
            except Exception as e:
                print(f"尝试切换到缓存窗口句柄时出错: {e}，将重新搜索。")

        # 2. 如果缓存句柄无效或页面不对，则遍历所有窗口查找
        if not found_chat_window:
            print("正在搜索所有打开的窗口以查找聊天页面...")
            for handle in driver.window_handles:
                driver.switch_to.window(handle)
                # 使用URL中的关键字来识别正确的聊天页面
                if "claude4.ai1.bar" in driver.current_url.lower():
                    chat_window_handle = handle  # 更新缓存的句柄
                    print(f"通过搜索找到并切换到聊天窗口: {driver.current_url}")
                    found_chat_window = True
                    break
        
        # 3. 如果遍历所有窗口后仍然找不到
        if not found_chat_window:
            print("错误：无法在所有打开的标签页中找到聊天窗口。")
            return jsonify({"error": "Chat window not found. Please ensure the chat page is open."}), 500

        # 修改：使用新的输入框选择器
        input_element = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "div[role='textbox'] > p"))
        )
        # 清空并输入消息
        try:
            driver.execute_script("arguments[0].textContent = '';", input_element)
        except:
            pass
        human_like_delay()
        input_element.send_keys(message)
        human_like_delay()
        
        # 修改：使用新的发送按钮选择器
        send_button = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, "button[aria-label='Send message']"))
        )
        human_like_click(send_button)
        human_like_delay()
        
        # 修改：先等待AI开始回复（Stop response 按钮出现），再等待结束（消失）
        try:
            WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "button[aria-label='Stop response']"))
            )
            print("已检测到 Stop response 按钮，AI开始回复")
        except Exception as e:
            print(f"未检测到 Stop response 开始按钮: {e}")
        WebDriverWait(driver, 360).until(
            EC.invisibility_of_element_located((By.CSS_SELECTOR, "button[aria-label='Stop response']"))
        )
        human_like_delay()

        # 修改：动态抓取所有“font-claude-response”节点并取最后一个
        try:
            response_elements = driver.find_elements(
                By.XPATH, "//div[starts-with(@class, 'font-claude-response')]"
            )
            if not response_elements:
                raise Exception("未找到任何 AI 回复元素")
            response_element = response_elements[-2]
            response_text = response_element.text or "已发送消息，但无法获取AI回复。请查看浏览器窗口中的回复。"
        except Exception as e:
            # 调试：打印错误和页面源码，便于定位
            print(f"获取AI回复失败: {e}")
            print("页面片段：", driver.page_source[:500])
            response_text = "已发送消息，但无法获取AI回复。请查看浏览器窗口中的回复。"

        return jsonify({"response": response_text})

    except Exception as e:
        # 处理任何错误
        print(f"处理聊天请求时出错: {str(e)}")
        return jsonify({"error": str(e)}), 500

# 启动Flask应用
if __name__ == '__main__':
    # 检查服务器是否已在运行
    if check_server_running(port=5000):
        print("服务器已在运行，无法重复启动")
        sys.exit(1)

    app.run(debug=False, port=5000, host='0.0.0.0')