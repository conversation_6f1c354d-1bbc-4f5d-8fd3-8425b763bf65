import requests
import json

# 定义聊天API的URL
url = "http://localhost:5000/chat"

# 定义请求头
headers = {
    'Content-Type': 'application/json'
}

# 定义要发送的消息
message = "请介绍AG2这个多智能体系统的内容"

# 创建JSON负载
data = {
    'content': message
}

# 发送POST请求
try: 
    print(f"正在发送消息: {message}")
    response = requests.post(
        url, 
        headers=headers, 
        data=json.dumps(data)
    )
    
    # 打印响应
    if response.status_code == 200:
        result = response.json()
        print("\nAI回复:")
        print(result.get('response', '未收到回复'))
    else:
        print(f"错误: {response.status_code}")
        print(response.text)
        
except Exception as e:
    print(f"发生错误: {str(e)}")
