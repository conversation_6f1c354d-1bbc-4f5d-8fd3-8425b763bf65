import requests
import json

# 定义聊天API的URL
url = "http://localhost:5000/chat"

# 定义请求头
headers = {
    'Content-Type': 'application/json'
}

# 定义要发送的消息 - 使用一个非常简单的消息
message = "hi"

# 创建JSON负载
data = {
    'content': message
}

# 发送POST请求
try:
    print(f"正在发送消息: {message}")
    response = requests.post(
        url, 
        headers=headers, 
        data=json.dumps(data),
        timeout=120  # 增加超时时间到2分钟
    )
    
    # 打印响应
    print(f"状态码: {response.status_code}")
    print(f"响应内容: {response.text[:200]}..." if len(response.text) > 200 else f"响应内容: {response.text}")
    
    if response.status_code == 200:
        try:
            result = response.json()
            print("\nAI回复:")
            print(result.get('response', '未收到回复'))
        except json.JSONDecodeError:
            print("响应不是有效的JSON格式")
    else:
        print(f"错误: {response.status_code}")
        
except Exception as e:
    print(f"发生错误: {str(e)}")
