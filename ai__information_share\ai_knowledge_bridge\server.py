"""
AI Knowledge Bridge MCP Server.

This is an MCP server implementation that runs the Flask server
for AI knowledge sharing between less knowledgeable and more knowledgeable AIs.
"""

import asyncio
import json
import os
import subprocess
import time
import requests

from mcp.server.models import InitializationOptions
import mcp.types as types
from mcp.server import NotificationOptions, Server
from pydantic import AnyUrl
import mcp.server.stdio

# Base URL for the Flask server
base_url = "http://localhost:5000"

# Request headers
headers = {
    'Content-Type': 'application/json'
}

# Store conversations as a simple key-value dict
conversations = {}
current_conversation_id = "default"

# Create the MCP server
server = Server("ai_knowledge_bridge")

def call_api(endpoint, data=None, timeout=60):
    """Call API and return results"""
    url = f"{base_url}/{endpoint}"

    try:
        if data:
            response = requests.post(url, headers=headers, data=json.dumps(data), timeout=timeout)
        else:
            response = requests.post(url, headers=headers, timeout=timeout)

        return response.status_code, response.json()
    except json.JSONDecodeError:
        return response.status_code, {"error": "Unable to parse JSON", "text": response.text}
    except Exception as e:
        return 500, {"error": str(e)}

@server.list_resources()
async def handle_list_resources() -> list[types.Resource]:
    """
    List available conversation resources.
    Each conversation is exposed as a resource with a custom conversation:// URI scheme.
    """
    return [
        types.Resource(
            uri=AnyUrl(f"conversation://{conv_id}"),
            name=f"Conversation: {conv_id}",
            description=f"A conversation with ID {conv_id}",
            mimeType="text/plain",
        )
        for conv_id in conversations
    ]

@server.read_resource()
async def handle_read_resource(uri: AnyUrl) -> str:
    """
    Read a specific conversation's content by its URI.
    The conversation ID is extracted from the URI host component.
    """
    if uri.scheme != "conversation":
        raise ValueError(f"Unsupported URI scheme: {uri.scheme}")

    conv_id = uri.host
    if conv_id in conversations:
        return conversations[conv_id]

    raise ValueError(f"Conversation not found: {conv_id}")

@server.list_prompts()
async def handle_list_prompts() -> list[types.Prompt]:
    """
    List available prompts.
    Each prompt can have optional arguments to customize its behavior.
    """
    return [
        types.Prompt(
            name="enhanced-question",
            description="Creates an enhanced question for the more knowledgeable AI",
            arguments=[
                types.PromptArgument(
                    name="search",
                    description="Whether to enable web search (true/false)",
                    required=False,
                ),
                types.PromptArgument(
                    name="thinking",
                    description="Whether to enable thinking (true/false)",
                    required=False,
                )
            ],
        )
    ]

@server.get_prompt()
async def handle_get_prompt(
    name: str, arguments: dict[str, str] | None
) -> types.GetPromptResult:
    """
    Generate a prompt by combining arguments with user question.
    The prompt includes feature preferences.
    """
    if name != "enhanced-question":
        raise ValueError(f"Unknown prompt: {name}")

    search = (arguments or {}).get("search", "false").lower() == "true"
    thinking = (arguments or {}).get("thinking", "false").lower() == "true"

    features = []
    if search:
        features.append("web search")
    if thinking:
        features.append("thinking")

    feature_text = ""
    if features:
        feature_text = f" Please use these features: {', '.join(features)}."

    return types.GetPromptResult(
        description="Enhanced question for the more knowledgeable AI",
        messages=[
            types.PromptMessage(
                role="user",
                content=types.TextContent(
                    type="text",
                    text=f"I need a comprehensive answer to this question.{feature_text} Please provide detailed information and explanations.",
                ),
            )
        ],
    )

@server.list_tools()
async def handle_list_tools() -> list[types.Tool]:
    """
    List available tools.
    Each tool specifies its arguments using JSON Schema validation.
    """
    return [
        types.Tool(
            name="start-conversation",
            description="Start a new conversation",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": [],
            },
        ),
        types.Tool(
            name="send-message",
            description="Send a message to the more knowledgeable AI",
            inputSchema={
                "type": "object",
                "properties": {
                    "content": {"type": "string"},
                },
                "required": ["content"],
            },
        ),
        types.Tool(
            name="activate-features",
            description="Activate specific features for the AI",
            inputSchema={
                "type": "object",
                "properties": {
                    "search": {"type": "boolean"},
                    "thinking": {"type": "boolean"},
                },
                "required": [],
            },
        ),
    ]

@server.call_tool()
async def handle_call_tool(
    name: str, arguments: dict | None
) -> list[types.TextContent | types.ImageContent | types.EmbeddedResource]:
    """
    Handle tool execution requests.
    Tools can modify server state and notify clients of changes.
    """
    global current_conversation_id

    if name == "start-conversation":
        # Start a new conversation
        status_code, result = call_api("new_conversation")

        if status_code == 200 and result.get("status") == "success":
            # Create a new conversation ID
            current_conversation_id = f"conv_{int(time.time())}"
            conversations[current_conversation_id] = "=== New Conversation ===\n\n"

            # Notify clients that resources have changed
            await server.request_context.session.send_resource_list_changed()

            return [
                types.TextContent(
                    type="text",
                    text=f"Started new conversation with ID: {current_conversation_id}",
                )
            ]
        else:
            error_message = f"Failed to start new conversation: {result}"
            return [
                types.TextContent(
                    type="text",
                    text=error_message,
                )
            ]

    elif name == "activate-features":
        if not arguments:
            arguments = {}

        search = arguments.get("search", True)
        thinking = arguments.get("thinking", True)

        features_data = {
            'search': search,
            'thinking': thinking
        }

        status_code, result = call_api("activate_features", features_data)

        if status_code == 200 and result.get("status") == "success":
            features = []
            if search:
                features.append("web search")
            if thinking:
                features.append("thinking")

            feature_text = "No features" if not features else ", ".join(features)

            # Add to the current conversation
            if current_conversation_id in conversations:
                conversations[current_conversation_id] += f"[System: Activated features: {feature_text}]\n\n"

            # Notify clients that resources have changed
            await server.request_context.session.send_resource_list_changed()

            return [
                types.TextContent(
                    type="text",
                    text=f"Activated features: {feature_text}",
                )
            ]
        else:
            error_message = f"Failed to activate features: {result}"
            return [
                types.TextContent(
                    type="text",
                    text=error_message,
                )
            ]

    elif name == "send-message":
        if not arguments:
            raise ValueError("Missing arguments")

        message = arguments.get("content")
        if not message:
            raise ValueError("Missing content")

        # Ensure we have a conversation to add to
        if current_conversation_id not in conversations:
            current_conversation_id = f"conv_{int(time.time())}"
            conversations[current_conversation_id] = "=== New Conversation ===\n\n"

        # Add the user message to the conversation
        conversations[current_conversation_id] += f"User: {message}\n\n"

        # Send the message to the AI
        chat_data = {
            'content': message
        }

        status_code, result = call_api("chat", chat_data, timeout=180)

        if status_code == 200:
            ai_response = result.get('response', 'No response received')

            # Add the AI response to the conversation
            conversations[current_conversation_id] += f"AI: {ai_response}\n\n"

            # Notify clients that resources have changed
            await server.request_context.session.send_resource_list_changed()

            return [
                types.TextContent(
                    type="text",
                    text=ai_response,
                )
            ]
        else:
            error_message = f"Failed to get a response: {result}"
            return [
                types.TextContent(
                    type="text",
                    text=error_message,
                )
            ]

    else:
        raise ValueError(f"Unknown tool: {name}")

def start_flask_server():
    """Start the Flask server in a new terminal."""
    # 首先检查Flask服务器是否已在运行
    try:
        response = requests.get(f"{base_url}/status", timeout=5)
        if response.status_code == 200 and response.json().get("ready", False):
            print("检测到Flask服务器已在运行，无需重复启动")
            return True
    except requests.exceptions.RequestException:
        pass
    
    # Get the absolute path to the flask_aichat.py script
    flask_script_path = os.path.abspath(os.path.join(os.path.dirname(os.path.dirname(__file__)), "flask_aichat.py"))

    # Command to run in a new terminal
    command = f"start cmd /k python {flask_script_path}"

    print(f"Starting Flask server in a new terminal: {flask_script_path}")
    # Execute the command to open a new terminal and run the Flask server
    subprocess.Popen(command, shell=True)

    # Wait for the Flask server to be ready
    print("Waiting for Flask server to be ready...")
    max_retries = 30
    retry_count = 0

    while retry_count < max_retries:
        try:
            # Check the status endpoint
            response = requests.get(f"{base_url}/status", timeout=2)
            if response.status_code == 200 and response.json().get("ready", False):
                print("Flask server is ready!")
                return True
        except requests.exceptions.RequestException:
            pass

        retry_count += 1
        print(f"Waiting for Flask server... ({retry_count}/{max_retries})")
        time.sleep(1)

    print("Warning: Flask server did not report ready status within the timeout period.")
    return False

async def main():
    """Run the MCP server using stdin/stdout streams."""
    # Start the Flask server directly (not in a thread)
    print("Starting Flask server...")
    flask_ready = start_flask_server()

    if not flask_ready:
        print("Warning: Proceeding with MCP server even though Flask server may not be ready")

    # Run the MCP server
    async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="ai_knowledge_bridge",
                server_version="0.1.0",
                capabilities=server.get_capabilities(
                    notification_options=NotificationOptions(),
                    experimental_capabilities={},
                ),
            ),
        )
