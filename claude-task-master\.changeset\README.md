# Changesets

This folder has been automatically generated by `@changesets/cli`, a build tool that works with multi-package repos or single-package repos to help version and publish code. Full documentation is available in the [Changesets repository](https://github.com/changesets/changesets).

## What are Changesets?

Changesets are a way to track changes to packages in your repository. Each changeset:

- Describes the changes you've made
- Specifies the type of version bump needed (patch, minor, or major)
- Connects these changes with release notes
- Automates the versioning and publishing process

## How to Use Changesets in Task Master

### 2. Making Changes

1. Create a new branch for your changes
2. Make your code changes
3. Write tests and ensure all tests pass

### 3. Creating a Changeset

After making changes, create a changeset by running:

```bash
npx changeset
```

This will:

- Walk you through a CLI to describe your changes
- Ask you to select impact level (patch, minor, major)
- Create a markdown file in the `.changeset` directory

### 4. Impact Level Guidelines

When choosing the impact level for your changes:

- **Patch**: Bug fixes and minor changes that don't affect how users interact with the system
  - Example: Fixing a typo in output text, optimizing code without changing behavior
- **Minor**: New features or enhancements that don't break existing functionality
  - Example: Adding a new flag to an existing command, adding new task metadata fields
- **Major**: Breaking changes that require users to update their usage
  - Example: Renaming a command, changing the format of the tasks.json file

### 5. Writing Good Changeset Descriptions

Your changeset description should:

- Be written for end-users, not developers
- Clearly explain what changed and why
- Include any migration steps or backward compatibility notes
- Reference related issues or pull requests with `#issue-number`

Examples:

```md
# Good

Added new `--research` flag to the `expand` command that uses Perplexity AI
to provide research-backed task expansions. Requires PERPLEXITY_API_KEY
environment variable.

# Not Good

Fixed stuff and added new flag
```

### 6. Committing Your Changes

Commit both your code changes and the generated changeset file:

```bash
git add .
git commit -m "Add feature X with changeset"
git push
```

### 7. Pull Request Process

1. Open a pull request
2. Ensure CI passes
3. Await code review
4. Once approved and merged, your changeset will be used during the next release

## Release Process (for Maintainers)

When it's time to make a release:

1. Ensure all desired changesets are merged
2. Run `npx changeset version` to update package versions and changelog
3. Review and commit the changes
4. Run `npm publish` to publish to npm

This can be automated through Github Actions

## Common Issues and Solutions

- **Merge Conflicts in Changeset Files**: Resolve just like any other merge conflict
- **Multiple Changes in One PR**: Create multiple changesets if changes affect different areas
- **Accidentally Committed Without Changeset**: Create the changeset after the fact and commit it separately

## Additional Resources

- [Changesets Documentation](https://github.com/changesets/changesets)
- [Common Questions](https://github.com/changesets/changesets/blob/main/docs/common-questions.md)
