## ✨ New Feature

### 📋 Feature Description
<!-- Brief description -->

### 🎯 Problem Statement
<!-- What problem does this feature solve? Why is it needed? -->

### 💡 Solution
<!-- How does this feature solve the problem? What's the approach? -->

### 🔗 Related Issues
<!-- Link related issues: Fixes #123, Part of #456 -->

## How to Use It

### Quick Start
```bash
# Basic usage example
```

### Example
<!-- Show a real use case -->
```bash
# Practical example
```

**What you should see:**
<!-- Expected behavior -->

## Contributor Checklist
- [ ] Created changeset: `npm run changeset`
- [ ] Tests pass: `npm test`
- [ ] Format check passes: `npm run format-check`
- [ ] Addressed CodeRabbit comments
- [ ] Added tests for new functionality
- [ ] Manually tested in CLI mode
- [ ] Manually tested in MCP mode (if applicable)

## Changelog Entry
<!-- One-liner for release notes -->

---

### For Maintainers

- [ ] Feature aligns with project vision
- [ ] CIs pass
- [ ] Changeset file exists
