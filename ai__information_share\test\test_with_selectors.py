import requests
import json
import time

# 定义聊天API的URL
url = "http://localhost:5000/chat"

# 定义请求头
headers = {
    'Content-Type': 'application/json'
}

# 定义要发送的消息
message = "你好，请简单介绍一下你自己"

# 创建JSON负载
data = {
    'content': message
}

# 发送POST请求
try:
    print(f"正在发送消息: {message}")
    print("请稍等，这可能需要一些时间...")
    
    start_time = time.time()
    response = requests.post(
        url, 
        headers=headers, 
        data=json.dumps(data),
        timeout=180  # 增加超时时间到3分钟
    )
    end_time = time.time()
    
    print(f"请求耗时: {end_time - start_time:.2f} 秒")
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        try:
            result = response.json()
            print("\nAI回复:")
            print(result.get('response', '未收到回复'))
        except json.JSONDecodeError:
            print("响应不是有效的JSON格式")
            print(f"响应内容: {response.text[:200]}..." if len(response.text) > 200 else f"响应内容: {response.text}")
    else:
        print(f"错误: {response.status_code}")
        print(f"响应内容: {response.text[:200]}..." if len(response.text) > 200 else f"响应内容: {response.text}")
        
except Exception as e:
    print(f"发生错误: {str(e)}")
