name: Pre-Release (RC)

on:
  workflow_dispatch: # Allows manual triggering from GitHub UI/API

concurrency: pre-release-${{ github.ref_name }}
jobs:
  rc:
    runs-on: ubuntu-latest
    # Only allow pre-releases on non-main branches
    if: github.ref != 'refs/heads/main'
    environment: extension-release
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: "npm"

      - name: Cache node_modules
        uses: actions/cache@v4
        with:
          path: |
            node_modules
            */*/node_modules
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Install dependencies
        run: npm ci
        timeout-minutes: 2

      - name: Enter RC mode (if not already in RC mode)
        run: |
          # Check if we're in pre-release mode with the "rc" tag
          if [ -f .changeset/pre.json ]; then
            MODE=$(jq -r '.mode' .changeset/pre.json 2>/dev/null || echo '')
            TAG=$(jq -r '.tag' .changeset/pre.json 2>/dev/null || echo '')
            
            if [ "$MODE" = "exit" ]; then
              echo "Pre-release mode is in 'exit' state, re-entering RC mode..."
              npx changeset pre enter rc
            elif [ "$MODE" = "pre" ] && [ "$TAG" != "rc" ]; then
              echo "In pre-release mode but with wrong tag ($TAG), switching to RC..."
              npx changeset pre exit
              npx changeset pre enter rc
            elif [ "$MODE" = "pre" ] && [ "$TAG" = "rc" ]; then
              echo "Already in RC pre-release mode"
            else
              echo "Unknown mode state: $MODE, entering RC mode..."
              npx changeset pre enter rc
            fi
          else
            echo "No pre.json found, entering RC mode..."
            npx changeset pre enter rc
          fi

      - name: Version RC packages
        run: npx changeset version
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}

      - name: Create Release Candidate Pull Request or Publish Release Candidate to npm
        uses: changesets/action@v1
        with:
          publish: node ./.github/scripts/pre-release.mjs
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
          VSCE_PAT: ${{ secrets.VSCE_PAT }}
          OVSX_PAT: ${{ secrets.OVSX_PAT }}

      - name: Commit & Push changes
        uses: actions-js/push@master
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          branch: ${{ github.ref }}
          message: "chore: rc version bump"
